# Test Plan: Offline Message Duplication Fix

## Issue Description
When sending a message while offline, the message gets duplicated when coming back online - showing 2 copies until the app is refreshed.

## Root Cause
The `attemptSendMessage` method was sending messages via both:
1. HTTP API (for persistence)
2. WebSocket (for real-time delivery)

When the WebSocket message was echoed back from the server, it was being added as a duplicate message.

## Fix Applied
1. **Removed WebSocket sending from `attemptSendMessage`**: Messages are now only sent via HTTP API. The server handles WebSocket broadcasting to other users.

2. **Improved duplicate detection**: Enhanced the duplicate detection logic to check by message ID when available, falling back to content/sender/timestamp comparison.

## Test Steps

### Test 1: Basic Offline Message Sending
1. Start the desktop messenger
2. Log in with a user account
3. Open a chat with another user
4. Disconnect from internet (disable WiFi/ethernet)
5. Type and send a message
6. Verify message shows as "queued" status
7. Reconnect to internet
8. Verify message is sent successfully
9. **Expected**: Only ONE copy of the message should appear
10. **Expected**: Message status should change from "queued" to "sent"

### Test 2: Multiple Offline Messages
1. Go offline
2. Send 3-4 different messages
3. Verify all show as "queued"
4. Go back online
5. **Expected**: All messages should be sent without duplication

### Test 3: Real-time Messaging Still Works
1. Have two users online
2. Send messages between them
3. **Expected**: Messages should appear in real-time on both sides
4. **Expected**: No duplicates should appear

### Test 4: Mixed Online/Offline Scenarios
1. Start online, send a message (should work normally)
2. Go offline, send a message (should queue)
3. Go online, send another message (should send immediately)
4. **Expected**: All messages appear once, in correct order

## Code Changes Made

### File: `desktop-messenger/renderer/scripts/chat.js`

#### Change 1: Removed WebSocket sending from attemptSendMessage
```javascript
// OLD CODE (lines 714-718):
if (response.ok) {
    // Send via WebSocket for real-time delivery
    if (wsManager.isConnected) {
        wsManager.sendMessage(this.currentRoomId, message.content);
    }

// NEW CODE:
if (response.ok) {
    // Don't send via WebSocket for our own messages - the API handles persistence
    // and other users will receive the message via WebSocket from the server
    // This prevents duplicate messages when the WebSocket echoes back our own message
```

#### Change 2: Enhanced duplicate detection
```javascript
// OLD CODE (lines 901-906):
const exists = this.messages.some(msg =>
    msg.content === message.content &&
    msg.senderId === message.senderId &&
    Math.abs(new Date(msg.timestamp) - new Date(message.timestamp)) < 5000
);

// NEW CODE:
const exists = this.messages.some(msg => {
    // If both messages have IDs, compare by ID
    if (msg.id && message.id) {
        return msg.id === message.id;
    }
    
    // Otherwise, compare by content, sender, and timestamp (within 5 seconds)
    return msg.content === message.content &&
           msg.senderId === message.senderId &&
           Math.abs(new Date(msg.timestamp) - new Date(message.timestamp)) < 5000;
});
```

## Expected Behavior After Fix
- Messages sent while offline should queue properly
- When back online, queued messages should send without duplication
- Real-time messaging between users should continue to work
- Message status indicators should work correctly
- No refresh should be needed to see correct message count
